"""
Kontext API 类型定义

定义 Kontext API 模块中使用的数据类型
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional, List
from datetime import datetime


@dataclass
class RemoteTaskResult:
    """远程任务结果"""
    task_id: str
    status: str
    images: List[str]
    metadata: Dict[str, Any]
    created_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None


@dataclass
class WorkflowConfig:
    """工作流配置"""
    workflow_file: str
    description: str
    image_count: int
    supports_controlnet: bool = False
    supports_redux: bool = False


@dataclass
class APIQuotaInfo:
    """API配额信息"""
    total_quota: int
    used_quota: int
    remaining_quota: int
    reset_time: Optional[datetime] = None
