"""
[二次开发] 统一消息发送器
整合所有消息发送、创建和格式化功能

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：提供统一的消息发送接口，支持多种消息格式和发送方式
- 维护者：开发团队
- 最后更新：2025-07-15
- 相关任务：消息处理系统重构
- 依赖关系：依赖message/models.py，被message/processor.py依赖
"""

import base64
import asyncio
import json
from typing import List, Optional, Dict, Any, Callable, Union
from .models import (
    MessageType, MessageContent, MessageFormat, MessageOptions, ProcessResult
)


class MessageSender:
    """统一消息发送器"""
    
    def __init__(self, logger=None):
        self.logger = logger
        self.default_options = MessageOptions()
    
    def _log(self, message: str, level: str = "info"):
        """日志记录"""
        if self.logger:
            if level == "error":
                self.logger.error(f"[MessageSender] {message}")
            elif level == "warning":
                self.logger.warning(f"[MessageSender] {message}")
            else:
                self.logger.info(f"[MessageSender] {message}")
    
    def create_text_message(self, content: str, options: Optional[MessageOptions] = None) -> Dict[str, Any]:
        """创建文本消息"""
        opts = options or self.default_options
        
        # 处理长文本截断
        if opts.truncate_long_text and len(content) > opts.max_text_length:
            content = content[:opts.max_text_length] + "..."
            self._log(f"文本内容已截断到{opts.max_text_length}字符")
        
        message = {
            "role": opts.role,
            "content": content,
            "type": MessageType.TEXT.value
        }
        
        # 添加可选信息
        if opts.show_timestamp:
            import time
            message["timestamp"] = str(time.time())
        
        return message
    
    def create_image_message(self, image_data: bytes, caption: str = "", 
                           options: Optional[MessageOptions] = None) -> Dict[str, Any]:
        """创建图片消息"""
        opts = options or self.default_options
        
        try:
            # 检查图片大小
            if len(image_data) > opts.max_image_size:
                error_msg = f"图片大小({len(image_data)}字节)超过限制({opts.max_image_size}字节)"
                self._log(error_msg, "error")
                return self.create_error_message(error_msg, options)
            
            # 将图片数据转换为base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # 创建图片内容
            image_content = {
                "type": "image_base64",
                "image_base64": f"data:image/{opts.image_format};base64,{image_base64}"
            }
            
            # 如果有说明文字，创建复合内容
            if caption:
                content = [
                    {"type": "text", "text": caption},
                    image_content
                ]
            else:
                content = [image_content]
            
            message = {
                "role": opts.role,
                "content": content,
                "type": MessageType.IMAGE.value if not caption else MessageType.MIXED.value
            }
            
            if opts.show_timestamp:
                import time
                message["timestamp"] = str(time.time())
            
            return message
            
        except Exception as e:
            error_msg = f"创建图片消息失败: {e}"
            self._log(error_msg, "error")
            return self.create_error_message(error_msg, options)
    
    def create_mixed_message(self, text: str, images: List[bytes], 
                           options: Optional[MessageOptions] = None) -> Dict[str, Any]:
        """创建混合消息（文本+图片）"""
        opts = options or self.default_options
        
        try:
            content = []
            
            # 添加文本内容
            if text.strip():
                if opts.truncate_long_text and len(text) > opts.max_text_length:
                    text = text[:opts.max_text_length] + "..."
                content.append({"type": "text", "text": text})
            
            # 添加图片内容
            for i, image_data in enumerate(images):
                if len(image_data) > opts.max_image_size:
                    self._log(f"跳过第{i+1}张图片，大小超限", "warning")
                    continue
                
                image_base64 = base64.b64encode(image_data).decode('utf-8')
                content.append({
                    "type": "image_base64",
                    "image_base64": f"data:image/{opts.image_format};base64,{image_base64}"
                })
            
            message = {
                "role": opts.role,
                "content": content,
                "type": MessageType.MIXED.value
            }
            
            if opts.show_timestamp:
                import time
                message["timestamp"] = str(time.time())
            
            return message
            
        except Exception as e:
            error_msg = f"创建混合消息失败: {e}"
            self._log(error_msg, "error")
            return self.create_error_message(error_msg, options)
    
    def create_progress_message(self, progress: int, total: int, 
                              message: str = "处理中...", 
                              options: Optional[MessageOptions] = None) -> Dict[str, Any]:
        """创建进度消息"""
        opts = options or self.default_options
        
        if total == 0:
            percentage = 0
        else:
            percentage = int((progress / total) * 100)
        
        progress_bar = self._create_progress_bar(percentage)
        content = f"{message}\n{progress_bar} {percentage}% ({progress}/{total})"
        
        message_dict = {
            "role": opts.role,
            "content": content,
            "type": MessageType.PROGRESS.value,
            "progress": progress,
            "total": total,
            "percentage": percentage
        }
        
        if opts.show_timestamp:
            import time
            message_dict["timestamp"] = time.time()
        
        return message_dict
    
    def _create_progress_bar(self, percentage: int, width: int = 20) -> str:
        """创建进度条"""
        filled = int(width * percentage / 100)
        empty = width - filled
        bar = "█" * filled + "░" * empty
        return f"[{bar}]"
    
    def create_error_message(self, error: str, options: Optional[MessageOptions] = None, 
                           suggestion: str = "", error_code: str = "") -> Dict[str, Any]:
        """创建错误消息"""
        opts = options or self.default_options
        
        content = f"❌ 处理失败: {error}"
        if suggestion:
            content += f"\n💡 建议: {suggestion}"
        
        message = {
            "role": opts.role,
            "content": content,
            "type": MessageType.ERROR.value,
            "error": error,
            "error_code": error_code
        }
        
        if suggestion:
            message["suggestion"] = suggestion
        
        if opts.show_timestamp:
            import time
            message["timestamp"] = time.time()
        
        return message
    
    def create_success_message(self, message: str, details: str = "", 
                             options: Optional[MessageOptions] = None) -> Dict[str, Any]:
        """创建成功消息"""
        opts = options or self.default_options
        
        content = f"✅ {message}"
        if details:
            content += f"\n📋 {details}"
        
        message_dict = {
            "role": opts.role,
            "content": content,
            "type": MessageType.SUCCESS.value
        }
        
        if details:
            message_dict["details"] = details
        
        if opts.show_timestamp:
            import time
            message_dict["timestamp"] = time.time()
        
        return message_dict
    
    def create_help_message(self, commands: Dict[str, str], 
                          options: Optional[MessageOptions] = None) -> Dict[str, Any]:
        """创建帮助消息"""
        opts = options or self.default_options
        
        content = "🤖 可用命令:\n\n"
        for command, description in commands.items():
            content += f"• **{command}**: {description}\n"
        
        message = {
            "role": opts.role,
            "content": content,
            "type": MessageType.HELP.value,
            "commands": commands
        }
        
        if opts.show_timestamp:
            import time
            message["timestamp"] = time.time()
        
        return message
    
    def create_status_message(self, status: Dict[str, Any], 
                            options: Optional[MessageOptions] = None) -> Dict[str, Any]:
        """创建状态消息"""
        opts = options or self.default_options
        
        content = "📊 系统状态:\n\n"
        for key, value in status.items():
            content += f"• **{key}**: {value}\n"
        
        message = {
            "role": opts.role,
            "content": content,
            "type": MessageType.STATUS.value,
            "status": status
        }
        
        if opts.show_timestamp:
            import time
            message["timestamp"] = time.time()
        
        return message
    
    def create_result_message(self, result: ProcessResult, 
                            options: Optional[MessageOptions] = None) -> Dict[str, Any]:
        """根据处理结果创建消息"""
        opts = options or self.default_options
        
        if result.has_error():
            return self.create_error_message(
                result.message, options, 
                suggestion=result.next_step,
                error_code=result.error_code
            )
        elif result.success:
            details = ""
            if opts.show_confidence and result.workflow_selected:
                confidence = result.metadata.get('confidence', 0)
                details = f"工作流: {result.workflow_type}, 置信度: {confidence:.1%}"
            
            return self.create_success_message(result.message, details, options)
        else:
            return self.create_text_message(result.message, options)
    
    def format_message(self, message: Dict[str, Any], format_type: MessageFormat) -> str:
        """格式化消息"""
        try:
            if format_type == MessageFormat.JSON:
                return json.dumps(message, ensure_ascii=False, indent=2)
            elif format_type == MessageFormat.MARKDOWN:
                return self._format_as_markdown(message)
            elif format_type == MessageFormat.HTML:
                return self._format_as_html(message)
            else:  # PLAIN
                return self._format_as_plain(message)
        except Exception as e:
            self._log(f"消息格式化失败: {e}", "error")
            return str(message.get('content', ''))
    
    def _format_as_markdown(self, message: Dict[str, Any]) -> str:
        """格式化为Markdown"""
        content = message.get('content', '')
        msg_type = message.get('type', MessageType.TEXT.value)
        
        if msg_type == MessageType.ERROR.value:
            return f"## ❌ 错误\n\n{content}"
        elif msg_type == MessageType.SUCCESS.value:
            return f"## ✅ 成功\n\n{content}"
        elif msg_type == MessageType.PROGRESS.value:
            return f"## 🔄 进度\n\n{content}"
        elif msg_type == MessageType.HELP.value:
            return f"## 🤖 帮助\n\n{content}"
        elif msg_type == MessageType.STATUS.value:
            return f"## 📊 状态\n\n{content}"
        else:
            return content
    
    def _format_as_html(self, message: Dict[str, Any]) -> str:
        """格式化为HTML"""
        content = message.get('content', '')
        msg_type = message.get('type', MessageType.TEXT.value)
        
        # 简单的HTML格式化
        content = content.replace('\n', '<br>')
        
        if msg_type == MessageType.ERROR.value:
            return f'<div class="error">❌ {content}</div>'
        elif msg_type == MessageType.SUCCESS.value:
            return f'<div class="success">✅ {content}</div>'
        elif msg_type == MessageType.PROGRESS.value:
            return f'<div class="progress">🔄 {content}</div>'
        else:
            return f'<div class="message">{content}</div>'
    
    def _format_as_plain(self, message: Dict[str, Any]) -> str:
        """格式化为纯文本"""
        return str(message.get('content', ''))
    
    async def send_with_retry(self, message: Dict[str, Any], send_func: Callable,
                            options: Optional[MessageOptions] = None, 
                            *args, **kwargs) -> bool:
        """带重试机制的消息发送"""
        opts = options or self.default_options
        
        for attempt in range(opts.max_retries):
            try:
                await asyncio.wait_for(
                    send_func(message, *args, **kwargs),
                    timeout=opts.timeout
                )
                self._log(f"消息发送成功 (尝试 {attempt + 1})")
                return True
                
            except asyncio.TimeoutError:
                error_msg = f"消息发送超时 (尝试 {attempt + 1}/{opts.max_retries})"
                self._log(error_msg, "warning")
                
            except Exception as e:
                error_msg = f"消息发送失败 (尝试 {attempt + 1}/{opts.max_retries}): {e}"
                self._log(error_msg, "warning")
            
            if attempt < opts.max_retries - 1:
                # 等待后重试
                await asyncio.sleep(opts.retry_delay * (attempt + 1))
            else:
                self._log("消息发送最终失败，已达到最大重试次数", "error")
                return False
        
        return False
    
    def batch_create_messages(self, contents: List[MessageContent], 
                            options: Optional[MessageOptions] = None) -> List[Dict[str, Any]]:
        """批量创建消息"""
        opts = options or self.default_options
        messages = []
        
        for content in contents:
            try:
                if content.get_type() == MessageType.MIXED:
                    message = self.create_mixed_message(content.text, content.images, opts)
                elif content.get_type() == MessageType.IMAGE:
                    message = self.create_image_message(content.images[0], options=opts)
                else:  # TEXT
                    message = self.create_text_message(content.text, opts)
                
                messages.append(message)
                
            except Exception as e:
                self._log(f"批量创建消息失败: {e}", "error")
                error_message = self.create_error_message(f"消息创建失败: {e}", opts)
                messages.append(error_message)
        
        return messages


# 全局实例
message_sender = MessageSender() 