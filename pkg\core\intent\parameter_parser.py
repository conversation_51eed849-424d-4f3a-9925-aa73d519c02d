"""
[二次开发] 用户输入参数解析器
支持解析用户输入中的特殊参数，如 --civitai, --seed, --lora 等

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：解析用户输入中的命令行风格参数
- 维护者：开发团队
- 最后更新：2025-07-15
- 相关任务：参数解析系统重构
- 依赖关系：被workflow/unified_parameter_service.py依赖
"""

import re
from typing import Dict, Any, Tuple, List, Optional
from dataclasses import dataclass


@dataclass
class ParsedParameters:
    """解析后的参数"""
    clean_prompt: str           # 清理后的提示词（移除参数）
    use_civitai: bool = False   # 是否使用Civitai搜索
    civitai_query: str = ""     # Civitai搜索关键词
    seed: Optional[int] = None  # 指定种子
    lora_names: List[str] = None  # 指定的LoRA模型名称
    lora_weights: Dict[str, float] = None  # LoRA权重
    quality: str = "standard"   # 质量级别：low, standard, high
    style: str = ""             # 风格参数
    aspect_ratio: str = ""      # 纵横比
    steps: Optional[int] = None # 生成步数
    cfg_scale: Optional[float] = None  # CFG引导强度
    use_again: bool = False     # 是否使用上一次的工作流
    no_trans: bool = False      # 是否禁用提示词润色，仅简单翻译
    show_help: bool = False     # 是否显示帮助信息
    help_type: str = "full"     # 帮助类型：full, brief, workflow
    raw_parameters: Dict[str, Any] = None  # 原始参数字典


class ParameterParser:
    """参数解析器"""
    
    def __init__(self):
        # 参数模式定义
        self.parameter_patterns = {
            'civitai': r'--civitai(?:\s+([^\s-]+))?',  # --civitai 或 --civitai architecture
            'seed': r'--seed\s+(\d+)',
            'lora': r'--lora\s+([^\s-]+)(?:\s+(\d*\.?\d+))?',  # --lora model_name [weight]
            'quality': r'--quality\s+(low|standard|high)',
            'style': r'--style\s+([^\s-]+)',
            'aspect_ratio': r'--(?:ar|aspect-ratio)\s+(\d+:\d+)',
            'steps': r'--steps\s+(\d+)',
            'cfg': r'--cfg\s+(\d*\.?\d+)',
            'again': r'--again(?=\s|$)',  # --again 重新生成上一次的工作流
            'repeat': r'--repeat(?=\s|$)',  # --repeat 重新生成上一次的工作流
            'retry': r'--retry(?=\s|$)',    # --retry 重新生成上一次的工作流
            'no_trans': r'--no-trans(?=\s|$)',  # --no-trans 禁用提示词润色，仅简单翻译
            'help': r'--help(?=\s|$)',      # --help 显示完整帮助信息
            'help_brief': r'--h(?=\s|$)',   # --h 显示简化帮助信息
        }
    
    def parse_user_input(self, user_input: str) -> ParsedParameters:
        """
        解析用户输入，提取参数和清理后的提示词
        
        Args:
            user_input: 用户原始输入
            
        Returns:
            ParsedParameters: 解析结果
        """
        if not user_input:
            return ParsedParameters(clean_prompt="")
        
        # 初始化结果
        params = ParsedParameters(
            clean_prompt=user_input,
            lora_names=[],
            lora_weights={},
            raw_parameters={}
        )
        
        # 解析各种参数
        remaining_text = user_input

        # 0. 解析 --again/--repeat/--retry 参数（优先检查）
        for again_keyword in ['again', 'repeat', 'retry']:
            again_match = re.search(self.parameter_patterns[again_keyword], remaining_text, re.IGNORECASE)
            if again_match:
                params.use_again = True
                params.raw_parameters[again_keyword] = True
                # 🔥 修复：保留again参数用于显示，不从remaining_text中移除
                break  # 只匹配第一个找到的关键词

        # 0.1 解析 --no-trans 参数
        no_trans_match = re.search(self.parameter_patterns['no_trans'], remaining_text, re.IGNORECASE)
        if no_trans_match:
            params.no_trans = True
            params.raw_parameters['no_trans'] = True
            remaining_text = re.sub(self.parameter_patterns['no_trans'], '', remaining_text, flags=re.IGNORECASE)

        # 0.2 解析 --help 和 --h 参数（优先检查）
        help_match = re.search(self.parameter_patterns['help'], remaining_text, re.IGNORECASE)
        help_brief_match = re.search(self.parameter_patterns['help_brief'], remaining_text, re.IGNORECASE)

        if help_match:
            params.show_help = True
            params.help_type = "full"
            params.raw_parameters['help'] = True
            # 保留help参数用于显示，不从remaining_text中移除
        elif help_brief_match:
            params.show_help = True
            params.help_type = "brief"
            params.raw_parameters['help_brief'] = True
            # 保留help参数用于显示，不从remaining_text中移除

        # 1. 解析 --civitai 参数
        civitai_match = re.search(self.parameter_patterns['civitai'], remaining_text, re.IGNORECASE)
        if civitai_match:
            params.use_civitai = True
            params.civitai_query = civitai_match.group(1) or ""
            params.raw_parameters['civitai'] = params.civitai_query
            remaining_text = re.sub(self.parameter_patterns['civitai'], '', remaining_text, flags=re.IGNORECASE)
        
        # 2. 解析 --seed 参数
        seed_match = re.search(self.parameter_patterns['seed'], remaining_text, re.IGNORECASE)
        if seed_match:
            try:
                params.seed = int(seed_match.group(1))
                params.raw_parameters['seed'] = params.seed
                remaining_text = re.sub(self.parameter_patterns['seed'], '', remaining_text, flags=re.IGNORECASE)
            except ValueError:
                pass
        
        # 3. 解析 --lora 参数（可能有多个）
        lora_matches = re.finditer(self.parameter_patterns['lora'], remaining_text, re.IGNORECASE)
        for match in lora_matches:
            lora_name = match.group(1)
            lora_weight = float(match.group(2)) if match.group(2) else 0.8
            params.lora_names.append(lora_name)
            params.lora_weights[lora_name] = lora_weight
        
        if params.lora_names:
            params.raw_parameters['lora'] = params.lora_weights
            remaining_text = re.sub(self.parameter_patterns['lora'], '', remaining_text, flags=re.IGNORECASE)
        
        # 4. 解析 --quality 参数
        quality_match = re.search(self.parameter_patterns['quality'], remaining_text, re.IGNORECASE)
        if quality_match:
            params.quality = quality_match.group(1).lower()
            params.raw_parameters['quality'] = params.quality
            remaining_text = re.sub(self.parameter_patterns['quality'], '', remaining_text, flags=re.IGNORECASE)
        
        # 5. 解析 --style 参数
        style_match = re.search(self.parameter_patterns['style'], remaining_text, re.IGNORECASE)
        if style_match:
            params.style = style_match.group(1)
            params.raw_parameters['style'] = params.style
            remaining_text = re.sub(self.parameter_patterns['style'], '', remaining_text, flags=re.IGNORECASE)
        
        # 6. 解析 --aspect-ratio 参数
        ar_match = re.search(self.parameter_patterns['aspect_ratio'], remaining_text, re.IGNORECASE)
        if ar_match:
            params.aspect_ratio = ar_match.group(1)
            params.raw_parameters['aspect_ratio'] = params.aspect_ratio
            remaining_text = re.sub(self.parameter_patterns['aspect_ratio'], '', remaining_text, flags=re.IGNORECASE)
        
        # 7. 解析 --steps 参数
        steps_match = re.search(self.parameter_patterns['steps'], remaining_text, re.IGNORECASE)
        if steps_match:
            try:
                params.steps = int(steps_match.group(1))
                params.raw_parameters['steps'] = params.steps
                remaining_text = re.sub(self.parameter_patterns['steps'], '', remaining_text, flags=re.IGNORECASE)
            except ValueError:
                pass
        
        # 8. 解析 --cfg 参数
        cfg_match = re.search(self.parameter_patterns['cfg'], remaining_text, re.IGNORECASE)
        if cfg_match:
            try:
                params.cfg_scale = float(cfg_match.group(1))
                params.raw_parameters['cfg_scale'] = params.cfg_scale
                remaining_text = re.sub(self.parameter_patterns['cfg'], '', remaining_text, flags=re.IGNORECASE)
            except ValueError:
                pass
        
        # 清理剩余文本
        params.clean_prompt = self._clean_text(remaining_text)
        
        return params
    
    def _clean_text(self, text: str) -> str:
        """清理文本，移除多余的空格和换行"""
        if not text:
            return ""
        
        # 移除多余的空格
        cleaned = re.sub(r'\s+', ' ', text)
        
        # 移除首尾空格
        cleaned = cleaned.strip()
        
        return cleaned
    
    def format_parameters_help(self) -> str:
        """格式化参数帮助信息"""
        help_text = """
🎛️ 支持的参数

📦 Civitai集成
--civitai [关键词]
  从Civitai搜索LoRA

🎯 模型控制
--seed 12345
  指定随机种子
--lora 模型名 [权重]
  指定LoRA模型

⚙️ 质量控制
--quality high
  设置生成质量
--steps 30
  设置生成步数
--cfg 7.5
  设置CFG引导强度

🎨 样式控制
--style realistic
  设置生成风格
--ar 16:9
  设置纵横比

⚡ 快速操作
--again
  重新生成上一次
--repeat
  重新生成上一次
--retry
  重新生成上一次

📝 提示词控制
--no-trans
  仅简单翻译中文
  不进行润色增强

❓ 帮助信息
--help
  显示完整帮助
--h
  显示简化帮助

💡 使用示例

基础使用
aigen 生成一只猫

高质量生成
aigen 现代建筑
  --quality high

指定种子
aigen 人像摄影
  --seed 12345

使用LoRA
aigen 动漫风格
  --lora 模型名

Civitai搜索
aigen 建筑设计
  --civitai architecture

重新生成
aigen --again

获取帮助
aigen --help
        """
        return help_text.strip()

    def format_brief_help(self) -> str:
        """格式化简化帮助信息"""
        help_text = """
🎛️ 常用参数

⚙️ 基础参数
--seed 12345
  指定随机种子
--quality high
  设置生成质量
--ar 16:9
  设置纵横比

⚡ 快速操作
--again
  重新生成上一次
--help
  显示完整帮助

💡 示例
aigen 生成一只猫
  --quality high
        """
        return help_text.strip()

    def format_workflow_help(self, workflow_type: str) -> str:
        """格式化工作流特定帮助信息"""
        if workflow_type.lower() == "aigen":
            return self._format_aigen_help()
        elif workflow_type.lower() in ["kontext", "kontext_api"]:
            return self._format_kontext_help()
        else:
            return self.format_parameters_help()

    def _format_aigen_help(self) -> str:
        """格式化Aigen工作流帮助"""
        help_text = """
🎨 Aigen工作流帮助

📋 工作流类型
• 纯文生图
  无图片输入
• 控制图+文生图
  上传草图、线稿
• 参考图+文生图
  上传风格参考图
• 混合模式
  控制图+参考图

⚙️ 常用参数
--seed 12345
  指定随机种子
--quality high
  设置生成质量
--lora 模型名
  指定LoRA模型
--civitai 关键词
  从Civitai搜索
--again
  重新生成上一次

💡 使用示例
基础生成
aigen 生成一只猫咪

高质量生成
aigen 现代建筑设计
  --quality high
  --seed 12345

使用LoRA
aigen 人像摄影
  --lora portrait_model

参考图生成
[上传参考图]
aigen 以这张图为参考

💡 提示
上传图片后系统会
自动选择合适工作流
        """
        return help_text.strip()

    def _format_kontext_help(self) -> str:
        """格式化Kontext工作流帮助"""
        help_text = """
🖼️ Kontext工作流帮助

📋 工作流类型
• 单图编辑
  1张图片输入
  风格转换或修改
• 双图编辑
  2张图片输入
  图像融合或对比
• 多图编辑
  3张图片输入
  复杂多图处理

⚙️ 常用参数
--seed 12345
  指定随机种子
--quality high
  设置生成质量
--ar 3:2
  设置纵横比
--again
  重新生成上一次

💡 使用示例
单图编辑
[上传1张图]
kontext 转换为油画风格

双图编辑
[上传2张图]
kontext 将第一张图的
风格应用到第二张图

多图编辑
[上传3张图]
kontext 融合这三张图
的元素创建新图片

云端API
kontext_api 处理图片
  --quality high

💡 提示
必须先上传图片
系统根据图片数量
自动选择工作流
        """
        return help_text.strip()


# 全局参数解析器实例
parameter_parser = ParameterParser()
