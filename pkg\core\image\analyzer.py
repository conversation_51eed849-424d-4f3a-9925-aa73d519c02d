"""
[二次开发] 图片分析器模块
提供图片内容分析、特征提取等功能

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：提供图片内容分析、特征提取和元数据处理功能
- 维护者：开发团队
- 最后更新：2025-07-15
- 相关任务：图片处理系统重构
- 依赖关系：依赖image/utils.py，被image/processor.py依赖
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any, List
from .utils import extract_image_metadata, detect_image_type, get_image_hash


@dataclass
class ImageInfo:
    """图片信息类"""
    data: bytes
    width: int = 0
    height: int = 0
    format: str = "unknown"
    size_bytes: int = 0
    size_mb: float = 0.0
    hash: str = ""
    aspect_ratio: float = 0.0
    
    def __post_init__(self):
        """初始化后自动填充信息"""
        if self.data and not self.hash:
            metadata = extract_image_metadata(self.data)
            self.width = metadata.get('width', 0)
            self.height = metadata.get('height', 0)
            self.format = metadata.get('format', 'unknown')
            self.size_bytes = metadata.get('size_bytes', 0)
            self.size_mb = metadata.get('size_mb', 0.0)
            self.hash = metadata.get('hash', '')
            
            # 计算宽高比
            if self.height > 0:
                self.aspect_ratio = self.width / self.height
    
    def is_portrait(self) -> bool:
        """是否是竖图"""
        return self.aspect_ratio < 1.0
    
    def is_landscape(self) -> bool:
        """是否是横图"""
        return self.aspect_ratio > 1.0
    
    def is_square(self, tolerance: float = 0.1) -> bool:
        """是否是方图"""
        return abs(self.aspect_ratio - 1.0) <= tolerance
    
    def get_orientation(self) -> str:
        """获取图片方向"""
        if self.is_square():
            return "square"
        elif self.is_portrait():
            return "portrait"
        else:
            return "landscape"


class ImageAnalyzer:
    """图片分析器"""
    
    def __init__(self, logger=None):
        self.logger = logger
    
    def _log(self, message: str, level: str = "info"):
        """日志记录"""
        if self.logger:
            if level == "error":
                self.logger.error(f"[ImageAnalyzer] {message}")
            elif level == "warning":
                self.logger.warning(f"[ImageAnalyzer] {message}")
            else:
                self.logger.info(f"[ImageAnalyzer] {message}")
    
    def analyze_image(self, image_data: bytes) -> ImageInfo:
        """
        分析单张图片
        
        Args:
            image_data: 图片二进制数据
            
        Returns:
            图片信息对象
        """
        try:
            return ImageInfo(data=image_data)
        except Exception as e:
            self._log(f"分析图片失败: {e}", "error")
            return ImageInfo(data=image_data)
    
    def analyze_images(self, images_data: List[bytes]) -> List[ImageInfo]:
        """
        分析多张图片
        
        Args:
            images_data: 图片数据列表
            
        Returns:
            图片信息列表
        """
        results = []
        for i, image_data in enumerate(images_data):
            try:
                info = self.analyze_image(image_data)
                results.append(info)
                self._log(f"分析第{i+1}张图片: {info.width}x{info.height}, {info.format}, {info.size_mb:.2f}MB")
            except Exception as e:
                self._log(f"分析第{i+1}张图片失败: {e}", "error")
                results.append(ImageInfo(data=image_data))
        
        return results
    
    def detect_best_aspect_ratio(self, images: List[ImageInfo], default: str = "1:1") -> str:
        """
        根据输入图片检测最佳输出宽高比
        
        Args:
            images: 图片信息列表
            default: 默认宽高比
            
        Returns:
            推荐的宽高比字符串
        """
        if not images:
            return default
        
        # 分析主要图片的方向
        orientations = [img.get_orientation() for img in images if img.width > 0 and img.height > 0]
        
        if not orientations:
            return default
        
        # 统计方向分布
        orientation_count = {}
        for orientation in orientations:
            orientation_count[orientation] = orientation_count.get(orientation, 0) + 1
        
        # 选择最常见的方向
        dominant_orientation = max(orientation_count.keys(), key=lambda x: orientation_count[x])
        
        # 根据主要方向推荐宽高比
        if dominant_orientation == "portrait":
            return "2:3"  # 竖图比例
        elif dominant_orientation == "landscape":
            return "3:2"  # 横图比例
        else:
            return "1:1"  # 方图比例
    
    def check_images_compatibility(self, images: List[ImageInfo], workflow_type: str) -> Dict[str, Any]:
        """
        检查图片与工作流的兼容性
        
        Args:
            images: 图片信息列表
            workflow_type: 工作流类型
            
        Returns:
            兼容性检查结果
        """
        result = {
            'compatible': True,
            'warnings': [],
            'errors': [],
            'recommendations': []
        }
        
        if not images:
            if workflow_type in ['kontext', 'kontext_api']:
                result['compatible'] = False
                result['errors'].append(f"{workflow_type} 工作流需要至少1张图片")
            return result
        
        # 检查图片数量
        if workflow_type == 'aigen' and len(images) > 1:
            result['warnings'].append("AIGEN工作流通常只使用第一张图片")
        elif workflow_type in ['kontext', 'kontext_api'] and len(images) > 3:
            result['warnings'].append("KONTEXT工作流最多支持3张图片，多余图片将被忽略")
        
        # 检查图片大小
        for i, img in enumerate(images):
            if img.size_mb > 10:
                result['warnings'].append(f"第{i+1}张图片过大 ({img.size_mb:.1f}MB)，建议压缩")
            
            if img.width == 0 or img.height == 0:
                result['errors'].append(f"第{i+1}张图片无法解析尺寸信息")
                result['compatible'] = False
        
        # 工作流特定检查
        if workflow_type == 'aigen':
            # AIGEN通常用于参考，对图片要求不严格
            result['recommendations'].append("AIGEN工作流将使用图片作为参考生成新图片")
        
        elif workflow_type in ['kontext', 'kontext_api']:
            # KONTEXT需要合适的图片进行转换
            avg_aspect_ratio = sum(img.aspect_ratio for img in images if img.aspect_ratio > 0) / len(images)
            if avg_aspect_ratio > 0:
                recommended_ratio = self.detect_best_aspect_ratio(images)
                result['recommendations'].append(f"推荐输出宽高比: {recommended_ratio}")
        
        return result
    
    def suggest_parameters(self, images: List[ImageInfo], workflow_type: str) -> Dict[str, Any]:
        """
        根据图片分析结果建议参数
        
        Args:
            images: 图片信息列表
            workflow_type: 工作流类型
            
        Returns:
            建议参数字典
        """
        suggestions = {}
        
        if not images:
            return suggestions
        
        # 通用参数建议
        avg_width = sum(img.width for img in images if img.width > 0) / len(images)
        avg_height = sum(img.height for img in images if img.height > 0) / len(images)
        
        if avg_width > 0 and avg_height > 0:
            # 建议输出尺寸
            if avg_width >= 1024 or avg_height >= 1024:
                suggestions['target_size'] = "1024x1024"  # 高质量
            else:
                suggestions['target_size'] = "512x512"   # 标准质量
        
        # 工作流特定建议
        if workflow_type == 'aigen':
            suggestions['strength'] = 0.8  # 参考强度
            suggestions['cfg_scale'] = 7.5  # 引导强度
        
        elif workflow_type in ['kontext', 'kontext_api']:
            # 根据图片复杂度建议参数
            total_pixels = sum(img.width * img.height for img in images if img.width > 0)
            if total_pixels > 1000000:  # 高分辨率图片
                suggestions['denoise'] = 0.75
            else:
                suggestions['denoise'] = 0.85
            
            # 建议宽高比
            suggestions['aspect_ratio'] = self.detect_best_aspect_ratio(images)
        
        return suggestions
    
    def find_duplicate_images(self, images: List[ImageInfo]) -> List[List[int]]:
        """
        查找重复图片
        
        Args:
            images: 图片信息列表
            
        Returns:
            重复图片的索引组列表
        """
        duplicates = []
        processed = set()
        
        for i, img1 in enumerate(images):
            if i in processed:
                continue
            
            duplicate_group = [i]
            for j, img2 in enumerate(images[i+1:], i+1):
                if j in processed:
                    continue
                
                # 比较哈希值
                if img1.hash and img2.hash and img1.hash == img2.hash:
                    duplicate_group.append(j)
                    processed.add(j)
            
            if len(duplicate_group) > 1:
                duplicates.append(duplicate_group)
                processed.update(duplicate_group)
        
        return duplicates 