# --help 功能使用示例

## 📖 基础使用示例

### 1. 获取完整帮助信息
```
用户: --help
机器人: 
🎛️ **支持的参数**

**Civitai集成**:
  --civitai [关键词]     从Civitai搜索并下载LoRA模型
  
**模型控制**:
  --seed 12345          指定随机种子
  --lora model_name [weight]  指定LoRA模型和权重
  
**质量控制**:
  --quality low|standard|high  设置生成质量
  --steps 30            设置生成步数
  --cfg 7.5             设置CFG引导强度
  
**样式控制**:
  --style realistic     设置生成风格
  --ar 16:9             设置纵横比

**快速操作**:
  --again               重新生成上一次的图片
  --repeat              重新生成上一次的图片
  --retry               重新生成上一次的图片

**提示词控制**:
  --no-trans            仅简单翻译中文提示词，不进行润色增强

**帮助信息**:
  --help                显示完整的参数帮助信息
  --h                   显示简化的参数帮助信息

**使用示例**:
```
aigen 生成一只猫 --civitai animal --quality high --seed 12345
aigen 现代建筑 --civitai architecture --lora ASTRA_Flux_OC_Vbeta-2 0.9
aigen 人像摄影 --quality high --style realistic --ar 3:4
aigen --again          重新生成上一次的图片
aigen --repeat         重新生成上一次的图片
aigen --retry          重新生成上一次的图片
aigen --help           显示完整帮助信息
aigen --h              显示简化帮助信息
```
```

### 2. 获取简化帮助信息
```
用户: --h
机器人:
🎛️ **常用参数**

**基础参数**:
  --seed 12345          指定随机种子
  --quality high        设置生成质量 (low/standard/high)
  --ar 16:9             设置纵横比

**快速操作**:
  --again               重新生成上一次的图片
  --help                显示完整帮助信息

**示例**: aigen 生成一只猫 --quality high --seed 12345
```

## 🎨 工作流特定帮助示例

### 1. Aigen工作流帮助
```
用户: aigen --help
机器人:
🎨 **Aigen工作流帮助**

**工作流类型**:
• 纯文生图：无图片输入，仅文字描述
• 控制图+文生图：上传草图、线稿等控制图像
• 参考图+文生图：上传风格、内容参考图像
• 混合模式：控制图+参考图+文字描述

**常用参数**:
  --seed 12345          指定随机种子
  --quality high        设置生成质量
  --lora model_name     指定LoRA模型
  --civitai keyword     从Civitai搜索LoRA
  --again               重新生成上一次的图片

**使用示例**:
```
aigen 生成一只可爱的猫咪
aigen 现代建筑设计 --quality high --seed 12345
aigen 人像摄影 --lora portrait_model 0.8
[上传参考图] aigen 以这张图为参考，生成类似风格的图片
```

💡 **提示**: 上传图片后系统会自动选择合适的工作流类型
```

### 2. Kontext工作流帮助
```
用户: kontext --help
机器人:
🖼️ **Kontext工作流帮助**

**工作流类型**:
• 单图编辑：1张图片输入，进行风格转换或内容修改
• 双图编辑：2张图片输入，进行图像融合或对比编辑
• 多图编辑：3张图片输入，复杂的多图像处理

**常用参数**:
  --seed 12345          指定随机种子
  --quality high        设置生成质量
  --ar 3:2              设置纵横比
  --again               重新生成上一次的图片

**使用示例**:
```
[上传1张图] kontext 将这张图转换为油画风格
[上传2张图] kontext 将第一张图的风格应用到第二张图
[上传3张图] kontext 融合这三张图的元素创建新图片
kontext_api 使用云端API处理图片 --quality high
```

💡 **提示**: 必须先上传图片，系统根据图片数量自动选择工作流
```

## 🔄 混合使用示例

### 1. 带提示词的帮助请求
```
用户: aigen 生成一只猫 --help
机器人: [显示Aigen工作流帮助]
# 注意：用户的提示词"生成一只猫"会被保留，但优先显示帮助信息
```

### 2. 简化工作流帮助
```
用户: kontext 编辑图片 --h
机器人: [显示Kontext工作流的简化帮助信息]
```

### 3. API工作流帮助
```
用户: kontext_api --help
机器人: [显示Kontext API工作流帮助]
```

## 🎯 实际使用场景

### 场景1：新用户学习
```
新用户: 我想生成图片，但不知道怎么用参数
用户: --help
机器人: [显示完整参数帮助]
用户: 看起来很复杂，有简单的吗？
用户: --h
机器人: [显示简化帮助]
用户: 明白了！aigen 生成一只猫 --quality high
```

### 场景2：忘记参数语法
```
用户: 我想重新生成上一张图，忘记命令了
用户: --h
机器人: [显示简化帮助，包含--again参数]
用户: aigen --again
```

### 场景3：学习特定工作流
```
用户: 我想编辑图片，但不知道kontext怎么用
用户: kontext --help
机器人: [显示Kontext工作流详细帮助]
用户: [上传图片] kontext 转换为油画风格
```

### 场景4：快速查阅
```
用户: 种子参数怎么用来着？
用户: --h
机器人: [显示简化帮助，包含--seed参数]
用户: aigen 风景画 --seed 12345
```

## 💡 使用技巧

1. **快速查阅**: 使用 `--h` 获取常用参数的快速参考
2. **详细学习**: 使用 `--help` 获取完整的参数说明和示例
3. **工作流学习**: 在工作流名称后加 `--help` 获取特定帮助
4. **随时查询**: 帮助请求不会影响正常工作流，可以随时使用
5. **保留上下文**: 即使请求帮助，你的提示词也会被保留

## ⚠️ 注意事项

1. 帮助请求会优先处理，不会执行实际的图片生成
2. 可以在任何时候使用帮助功能，不需要特定的会话状态
3. 帮助内容会根据工作流上下文自动调整
4. 简化帮助适合快速查阅，完整帮助适合详细学习
