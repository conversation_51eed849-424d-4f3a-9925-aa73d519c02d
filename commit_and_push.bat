@echo off
echo 添加修改的文件到暂存区...
git add pkg/core/workflow/unified_routing_system.py
git add pkg/core/workflow/workflow_parameter_adapter.py
git add pkg/core/workflow/manager_base.py
git add pkg/workers/flux/flux_workflow_manager.py

echo 检查暂存区状态...
git status --short

echo 提交更改...
git commit -m "feat: 添加图片反推工作流支持

修复'aigen 图片反推'工作流无法正常调用的问题：

核心修改：
- 在WorkflowSubType枚举中添加AIGEN_IMAGE_TO_TEXT类型
- 更新工作流文件映射，将图片反推映射到image_to_text_workflow.json
- 添加_is_image_to_text_request方法识别图片反推请求
- 在_route_aigen_pipeline中添加图片反推检测逻辑
- 扩展WorkflowResult类，添加text_data字段支持文本输出
- 更新FluxWorkflowManager的_determine_workflow_subtype方法

技术细节：
- 图片反推通过ComfyUI工作流实现，不依赖LLM视觉理解
- 支持关键词：图片反推、反推、图片识别、描述图片等
- 工作流返回文本结果而非图片数据
- 完善了工作流参数适配器的类型转换映射

解决问题：
✅ 路由决策 - 能正确识别图片反推工作流
✅ 工作流执行 - 支持image_to_text_workflow.json执行
✅ 结果返回 - 支持文本结果返回"

echo 推送到远程仓库...
git push

echo 检查推送结果...
git log --oneline -1

echo 完成！
