{"26": {"inputs": {"images": ["31", 0]}, "class_type": "PreviewImage", "_meta": {"title": "final_image"}}, "28": {"inputs": {"upscale_model": ["47", 0], "image": ["79", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "29": {"inputs": {"sharpen_radius": 1, "sigma": 0.4000000000000001, "alpha": 0.4000000000000001, "image": ["28", 0]}, "class_type": "ImageSharpen", "_meta": {"title": "Image Sharpen"}}, "30": {"inputs": {"value": 0}, "class_type": "easy float", "_meta": {"title": "CN切换时机1"}}, "31": {"inputs": {"samples": ["76", 0], "vae": ["57", 0]}, "class_type": "VAEDecode", "_meta": {"title": "final_image_output"}}, "34": {"inputs": {"pixels": ["51", 0], "vae": ["57", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "37": {"inputs": {"strength": 0.7500000000000001, "start_percent": ["30", 0], "end_percent": ["54", 0], "positive": ["65", 0], "negative": ["69", 0], "control_net": ["49", 0], "image": ["53", 0], "vae": ["57", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "39": {"inputs": {"strength": 0.5500000000000002, "start_percent": ["54", 0], "end_percent": 0.7500000000000002, "positive": ["37", 0], "negative": ["37", 1], "control_net": ["49", 0], "image": ["53", 0], "vae": ["57", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "47": {"inputs": {"model_name": "8x_NMKD-Typescale_175k.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "49": {"inputs": {"type": "canny/lineart/anime_lineart/mlsd", "control_net": ["50", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "50": {"inputs": {"control_net_name": "FLUX.1-dev-ControlNet-Union-Pro-2.0.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "51": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 4.000000000000001, "image": ["29", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "Scale Image to Total Pixels"}}, "52": {"inputs": {"coeff": 1.2, "denoise": 0.5000000000000001}, "class_type": "GITSSchedulerFuncProvider", "_meta": {"title": "GITSScheduler Func Provider"}}, "53": {"inputs": {"preprocessor": "AnyLineArtPreprocessor_aux", "resolution": 2560, "image": ["83", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "54": {"inputs": {"value": 0.6000000000000001}, "class_type": "easy float", "_meta": {"title": "CN切换时机2"}}, "56": {"inputs": {"anything": ["31", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "57": {"inputs": {"vae_name": "Flux_ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "58": {"inputs": {"model_path": "svdq-int4-flux.1-dev", "cache_threshold": 0, "attention": "nunchaku-fp16", "cpu_offload": "auto", "device_id": 0, "data_type": "bfloat16", "i2f_mode": "enabled"}, "class_type": "NunchakuFluxDiTLoader", "_meta": {"title": "Nunchaku FLUX DiT Loader"}}, "60": {"inputs": {"delimiter": " ", "clean_whitespace": "true", "text_a": ["62", 0], "text_b": ["64", 2], "text_c": ["71", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "62": {"inputs": {"prompt": "A professional Architectural photography of"}, "class_type": "CR Prompt Text", "_meta": {"title": "⚙️ CR Prompt Text"}}, "63": {"inputs": {"model": "Florence-2-Flux-Large", "precision": "fp16", "attention": "sdpa", "convert_to_safetensors": false}, "class_type": "Florence2ModelLoader", "_meta": {"title": "Florence2ModelLoader"}}, "64": {"inputs": {"text_input": "", "task": "detailed_caption", "fill_mask": true, "keep_model_loaded": false, "max_new_tokens": 1024, "num_beams": 3, "do_sample": true, "output_mask_select": "", "seed": 1058303505425691, "image": ["79", 0], "florence2_model": ["63", 0]}, "class_type": "Florence2Run", "_meta": {"title": "Florence2Run"}}, "65": {"inputs": {"text": ["60", 0], "clip": ["66", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "66": {"inputs": {"model_type": "flux.1", "text_encoder1": "clip_l.safetensors", "text_encoder2": "t5xxl_fp8_e4m3fn.safetensors", "t5_min_length": 512}, "class_type": "NunchakuTextEncoderLoaderV2", "_meta": {"title": "Nunchaku Text Encoder Loader V2"}}, "69": {"inputs": {"conditioning": ["65", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "71": {"inputs": {"prompt": "(geometric composition:1.2), \nAs a professional architectural photographer using Phase One IQ4 150MP medium format system,\nSchneider Kreuznach 35mm f/4.5 tilt-shift lens, technical specifications:\n- f/8 aperture for full-depth coverage\n- ISO 64 native sensitivity\n- 1/125s shutter speed\n- Pixel Shift Multi-Shot mode enabled\n\nVisual requirements:\n- Accurate BRDF representation of glass materials\n- 0.5mm-scale concrete pore details\n- Golden hour light transition (5500K to 3200K)\n- medium format film grain\n- architectural lines refinement\n- atmospheric perspective enhancement\n- ultra-sharp focus\n- subtle HDR\n- cinematic lighting\n- texture perfection\n\nTechnical enhancements:\nHasselblad Natural Color Solution (HNCS v4.0)\n14-stop dynamic range optimization"}, "class_type": "CR Prompt Text", "_meta": {"title": "⚙️ CR Prompt Text"}}, "76": {"inputs": {"add_noise": true, "noise_seed": 797446794676751, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "start_at_step": 0, "end_at_step": 10000, "noise_mode": "GPU(=A1111)", "return_with_leftover_noise": false, "batch_seed_mode": "incremental", "variation_seed": 0, "variation_strength": 0, "variation_method": "linear", "internal_seed": 0, "model": ["58", 0], "positive": ["39", 0], "negative": ["69", 0], "latent_image": ["34", 0], "scheduler_func_opt": ["52", 0]}, "class_type": "KSamplerAdvanced //Inspire", "_meta": {"title": "KSamplerAdvanced (inspire)"}}, "79": {"inputs": {"base64_data": "", "image_output": "<PERSON>de", "save_prefix": "ComfyUI"}, "class_type": "easy loadImageBase64", "_meta": {"title": "load_image_input_01"}}, "83": {"inputs": {"width": 2560, "height": 2560, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "always", "multiple_of": 0, "image": ["84", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "84": {"inputs": {"base64_data": "", "image_output": "<PERSON>de", "save_prefix": "ComfyUI"}, "class_type": "easy loadImageBase64", "_meta": {"title": "load_image_input_02"}}}