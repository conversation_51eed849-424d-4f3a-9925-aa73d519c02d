"""
[二次开发] 核心会话管理模块
提供统一的会话管理接口，支持所有工作流类型

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：会话管理模块的统一导出接口
- 维护者：开发团队
- 最后更新：2025-07-15
- 相关任务：会话管理系统重构
- 依赖关系：导出会话管理模块的核心接口
"""

from .manager import SessionManager, session_manager
from .models import (
    WorkflowType, 
    SessionState, 
    WorkflowSession,
    SessionImage,
    FluxImageType,
    session_state_from_string
)
from .states import is_execution_command, is_cancel_command

__all__ = [
    'SessionManager',
    'session_manager', 
    'WorkflowType',
    'SessionState',
    'WorkflowSession',
    'SessionImage',
    'FluxImageType',
    'session_state_from_string',
    'is_execution_command',
    'is_cancel_command'
] 