"""
Kontext API模式工作流执行器
通过HTTP API调用远程/云端ComfyUI服务
"""

import json
import asyncio
import aiohttp
import base64
import os
import tempfile
from typing import Dict, Any, List, Optional
from .kontext_base_executor import BaseKontextExecutor
from pkg.core.workflow.manager_base import WorkflowResult


class ApiKontextExecutor(BaseKontextExecutor):
    def __init__(self, api_url: str = "https://api.comfyui.com", timeout: int = 180, api_key: str = None):
        self.api_url = api_url
        self.timeout = timeout
        self.api_key = api_key
        self.session = None
        self.current_workflow_data = None  # 🔥 新增：保存工作流数据供--again功能使用
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取或创建aiohttp会话"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            # 🔥 简化：不在HTTP头部添加认证信息，而是通过extra_data传递给ComfyUI API节点
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session
    
    async def close(self):
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def execute_workflow(self, prompt: str, query: Any, *args, **kwargs) -> WorkflowResult:
        try:
            # 1. 加载工作流文件
            workflow_data = await self._load_workflow_file(prompt)  # prompt作为workflow_file
            if not workflow_data:
                return WorkflowResult(success=False, error_message=f'工作流文件不存在: {prompt}')
            
            # 2. 更新工作流参数
            updated_workflow = self._update_workflow_params(workflow_data, query)
            
            # 3. 上传图片（如果有）
            if 'images' in query and query['images']:
                updated_workflow = await self._upload_images_to_workflow(updated_workflow, query['images'])

            # 🔥 修复：保存最终的工作流数据供--again功能使用
            self.current_workflow_data = updated_workflow

            # 4. 提交工作流到API
            session = await self._get_session()
            prompt_id = await self._submit_workflow(session, updated_workflow)
            if not prompt_id:
                return WorkflowResult(success=False, error_message='提交工作流失败')
            
            # 5. 等待执行完成
            image_data = await self._wait_for_completion(prompt_id)
            if not image_data:
                return WorkflowResult(success=False, error_message='工作流执行超时或失败')
            
            return WorkflowResult(success=True, image_data=image_data, metadata={
                'prompt_id': prompt_id,
                'workflow_file': prompt
            })
            
        except Exception as e:
            return WorkflowResult(success=False, error_message=str(e))
    
    async def _load_workflow_file(self, workflow_file: str) -> Optional[Dict[str, Any]]:
        """加载工作流文件"""
        try:
            # 检查是否是完整路径
            if os.path.exists(workflow_file):
                file_path = workflow_file
            else:
                # 假设是相对于workflows目录的路径
                file_path = os.path.join('workflows', workflow_file)
            
            if not os.path.exists(file_path):
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except Exception as e:
            print(f"加载工作流文件失败: {e}")
            return None
    
    def _update_workflow_params(self, workflow: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """更新工作流参数"""
        updated_workflow = workflow.copy()
        
        # 更新FluxKontextProImageNode参数（API模式主要使用这个节点）
        for node_id, node_data in updated_workflow.items():
            if node_data.get("class_type") == "FluxKontextProImageNode":
                if 'prompt' in params:
                    node_data["inputs"]["prompt"] = params['prompt']
                if 'aspect_ratio' in params:
                    node_data["inputs"]["aspect_ratio"] = params['aspect_ratio']
                if 'guidance' in params:
                    node_data["inputs"]["guidance"] = params['guidance']
                if 'steps' in params:
                    node_data["inputs"]["steps"] = params['steps']
                if 'seed' in params:
                    node_data["inputs"]["seed"] = params['seed']
                if 'prompt_upsampling' in params:
                    node_data["inputs"]["prompt_upsampling"] = params['prompt_upsampling']
                break
        
        # 更新CLIPTextEncode节点（如果有）
        if 'prompt' in params:
            for node_id, node_data in updated_workflow.items():
                if node_data.get("class_type") == "CLIPTextEncode":
                    # 检查是否是正面提示词节点
                    if "positive" in node_data.get("_meta", {}).get("title", "").lower():
                        node_data["inputs"]["text"] = params['prompt']
                    # 检查是否是负面提示词节点
                    elif "negative" in node_data.get("_meta", {}).get("title", "").lower():
                        node_data["inputs"]["text"] = params.get('negative_prompt', '')
        
        return updated_workflow
    
    async def _upload_images_to_workflow(self, workflow: Dict[str, Any], images: List[bytes]) -> Dict[str, Any]:
        """将图片上传到工作流"""
        try:
            session = await self._get_session()
            
            # 查找图片输入节点
            image_input_nodes = []
            for node_id, node_data in workflow.items():
                if node_data.get("class_type") in ["LoadImage", "easy loadImageBase64"]:
                    image_input_nodes.append((node_id, node_data))
            
            # 按标题排序，确保正确的映射顺序
            image_input_nodes.sort(key=lambda x: x[1].get("_meta", {}).get("title", ""))
            
            # 上传图片并更新工作流
            for i, (node_id, node_data) in enumerate(image_input_nodes):
                if i < len(images):
                    # 对于API模式，主要使用base64方式
                    if node_data.get("class_type") == "easy loadImageBase64":
                        # 将图片转换为base64
                        image_base64 = base64.b64encode(images[i]).decode('utf-8')
                        node_data["inputs"]["base64_data"] = image_base64
                    elif node_data.get("class_type") == "LoadImage":
                        # 上传图片到API
                        image_name = await self._upload_image(session, images[i], f"kontext_input_{i+1}")
                        if image_name:
                            node_data["inputs"]["image"] = image_name
            
            return workflow
            
        except Exception as e:
            print(f"上传图片失败: {e}")
            return workflow
    
    async def _upload_image(self, session: aiohttp.ClientSession, image_data: bytes, filename: str) -> Optional[str]:
        """上传单张图片到API"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_file:
                temp_file.write(image_data)
                temp_file_path = temp_file.name
            
            try:
                # 上传文件
                with open(temp_file_path, 'rb') as f:
                    data = aiohttp.FormData()
                    data.add_field('image', f, filename=filename)
                    
                    async with session.post(f"{self.api_url}/upload/image", data=data) as response:
                        if response.status == 200:
                            result = await response.json()
                            return result.get('name', filename)
                        else:
                            print(f"上传图片失败: {response.status}")
                            return None
            finally:
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    
        except Exception as e:
            print(f"上传图片出错: {e}")
            return None
    
    async def _submit_workflow(self, session: aiohttp.ClientSession, workflow: Dict[str, Any]) -> Optional[str]:
        """提交工作流到API"""
        try:
            # 🔥 关键修复：使用正确的字段名 api_key_comfy_org
            prompt_data = {"prompt": workflow}

            # 如果有API Key，添加到extra_data中（使用正确的字段名）
            if self.api_key:
                prompt_data["extra_data"] = {"api_key_comfy_org": self.api_key}
                print(f"🔑 已添加ComfyUI官方API Key到extra_data中（字段名：api_key_comfy_org）")

            async with session.post(
                f"{self.api_url}/prompt",
                json=prompt_data,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('prompt_id')
                else:
                    error_text = await response.text()
                    print(f"提交工作流失败: {response.status} - {error_text}")
                    return None
        except Exception as e:
            print(f"提交工作流出错: {e}")
            return None
    
    async def _wait_for_completion(self, prompt_id: str) -> Optional[bytes]:
        """等待工作流完成并获取图片数据"""
        try:
            session = await self._get_session()
            start_time = asyncio.get_event_loop().time()
            
            while asyncio.get_event_loop().time() - start_time < self.timeout:
                # 检查工作流状态
                async with session.get(f"{self.api_url}/history/{prompt_id}") as response:
                    if response.status == 200:
                        result = await response.json()
                        print(f"🔍 [DEBUG] History API响应: {result}")

                        if prompt_id in result:
                            prompt_data = result[prompt_id]
                            print(f"🔍 [DEBUG] Prompt数据结构: {list(prompt_data.keys())}")

                            # 检查多种可能的状态字段
                            status = prompt_data.get('status', {})
                            status_str = status.get('status_str', 'unknown')

                            # 也检查是否有outputs字段（表示完成）
                            outputs = prompt_data.get('outputs', {})
                            has_outputs = bool(outputs)

                            print(f"🔍 [DEBUG] 状态检查: status_str={status_str}, has_outputs={has_outputs}")

                            if status_str == 'error':
                                print(f"❌ 工作流执行出错: {prompt_id}")
                                return None
                            elif status_str == 'success' or has_outputs:
                                # 🔥 修复：优先选择正确的输出节点，避免下载输入图片
                                print(f"✅ 工作流已完成，开始获取输出图片")
                                print(f"🔍 [DEBUG] Kontext API工作流输出节点: {list(outputs.keys()) if outputs else '无'}")

                                # 优先选择节点（基于kontext_api_1image.json结构）
                                preferred_output_nodes = ['90', '83']  # 90是PreviewImage，83是FluxKontextProImageNode
                                fallback_nodes = []

                                # 详细记录所有节点的输出情况
                                for node_id, node_output in outputs.items():
                                    print(f"🔍 [DEBUG] 节点{node_id}: {node_output}")
                                    if 'images' in node_output:
                                        images = node_output['images']
                                        if images and len(images) > 0:
                                            filename = images[0]['filename']
                                            image_type = images[0].get('type', 'temp')
                                            print(f"🔍 [DEBUG] 节点{node_id}有图片输出: {filename} (类型: {image_type})")

                                            if node_id in preferred_output_nodes:
                                                # 优先节点，直接下载
                                                print(f"✅ 从优先输出节点{node_id}下载Kontext图片: {filename}")
                                                return await self._download_image(session, filename)
                                            else:
                                                # 备用节点，记录但不立即下载
                                                fallback_nodes.append((node_id, node_output))

                                # 如果没有找到优先节点，尝试备用节点
                                if fallback_nodes:
                                    print(f"⚠️ 优先节点无图片输出，尝试{len(fallback_nodes)}个备用节点")
                                    for node_id, node_output in fallback_nodes:
                                        images = node_output['images']
                                        if images and len(images) > 0:
                                            filename = images[0]['filename']
                                            print(f"📦 从备用节点{node_id}下载Kontext图片: {filename}")
                                            return await self._download_image(session, filename)

                                print(f"❌ 没有找到任何有图片输出的节点")
                                return None
                    
                # 🔥 修复：使用智能轮询间隔（基于6c49d51e的成功实现）
                current_time = asyncio.get_event_loop().time() - start_time

                # 智能轮询间隔：前30秒每2秒，30-60秒每5秒，60秒后每10秒
                if current_time < 30:
                    poll_interval = 2
                elif current_time < 60:
                    poll_interval = 5
                else:
                    poll_interval = 10

                print(f"🔍 [DEBUG] 工作流状态检查中... 已等待 {current_time:.1f}s，下次检查间隔: {poll_interval}s")
                await asyncio.sleep(poll_interval)
            
            print(f"工作流执行超时: {prompt_id}")
            return None
            
        except Exception as e:
            print(f"等待工作流完成出错: {e}")
            return None
    
    async def _download_image(self, session: aiohttp.ClientSession, filename: str) -> Optional[bytes]:
        """下载生成的图片"""
        try:
            download_url = f"{self.api_url}/view?filename={filename}&type=output"
            print(f"🔍 [DEBUG] 下载图片URL: {download_url}")

            async with session.get(download_url) as response:
                print(f"🔍 [DEBUG] 下载响应状态: {response.status}")
                if response.status == 200:
                    image_data = await response.read()
                    print(f"✅ 成功下载图片，大小: {len(image_data)} bytes")
                    return image_data
                else:
                    error_text = await response.text()
                    print(f"❌ 下载图片失败: {response.status} - {error_text}")
                    return None
        except Exception as e:
            print(f"❌ 下载图片出错: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return None