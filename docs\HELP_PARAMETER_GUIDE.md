# --help 参数功能指南

## 功能概述

新增的 `--help` 参数功能为用户提供了便捷的帮助信息获取方式，支持多种触发方式和上下文相关的帮助内容。

## 🎯 支持的触发方式

### 1. 完整帮助
```
--help                    # 显示完整的参数帮助信息
aigen --help              # 显示Aigen工作流的完整帮助
kontext --help            # 显示Kontext工作流的完整帮助
kontext_api --help        # 显示Kontext API工作流的完整帮助
```

### 2. 简化帮助
```
--h                       # 显示简化的参数帮助信息
aigen --h                 # 显示Aigen工作流的简化帮助
kontext --h               # 显示Kontext工作流的简化帮助
```

## 📋 帮助内容类型

### 1. 通用参数帮助
当用户发送 `--help` 或 `--h` 时，显示所有支持的参数说明：

- **Civitai集成**: `--civitai` 参数使用说明
- **模型控制**: `--seed`、`--lora` 等参数
- **质量控制**: `--quality`、`--steps`、`--cfg` 等参数
- **样式控制**: `--style`、`--ar` 等参数
- **快速操作**: `--again`、`--repeat`、`--retry` 等参数
- **提示词控制**: `--no-trans` 等参数
- **帮助信息**: `--help`、`--h` 使用说明
- **使用示例**: 实际使用案例

### 2. 工作流特定帮助
当用户在工作流上下文中请求帮助时，显示针对性内容：

#### Aigen工作流帮助
- 工作流类型说明（纯文生图、控制图+文生图等）
- 常用参数介绍
- 使用示例和技巧

#### Kontext工作流帮助
- 工作流类型说明（单图编辑、双图编辑等）
- 图片处理相关参数
- 使用示例和注意事项

### 3. 简化帮助
显示最常用的参数和快速示例，适合快速查阅。

## 🚀 使用示例

### 基础使用
```
用户: --help
机器人: [显示完整的参数帮助信息]

用户: --h
机器人: [显示简化的参数帮助信息]
```

### 工作流上下文帮助
```
用户: aigen --help
机器人: [显示Aigen工作流专用帮助]

用户: kontext --h
机器人: [显示Kontext工作流简化帮助]

用户: kontext_api --help
机器人: [显示Kontext API工作流帮助]
```

### 混合使用
```
用户: aigen 生成一只猫 --help
机器人: [显示Aigen工作流帮助，同时保留用户的提示词]

用户: kontext 编辑图片 --h
机器人: [显示Kontext工作流简化帮助]
```

## 🔧 技术实现

### 参数解析
- 在 `parameter_parser.py` 中添加了 `help` 和 `help_brief` 参数模式
- 扩展了 `ParsedParameters` 类，添加 `show_help` 和 `help_type` 字段
- 实现了多种帮助内容生成方法

### 处理流程
1. **参数检测**: 在用户输入中检测 `--help` 或 `--h` 参数
2. **上下文分析**: 根据用户输入确定工作流上下文
3. **内容生成**: 根据帮助类型和上下文生成相应的帮助内容
4. **消息返回**: 将帮助内容作为响应消息返回给用户

### 集成点
- **ComfyUI Agent**: 在工作流处理前检查帮助请求
- **消息处理器**: 在新请求处理中检查帮助请求
- **参数解析器**: 提供帮助内容生成功能

## 💡 设计特点

### 1. 上下文感知
- 根据用户输入中的工作流关键词提供针对性帮助
- 支持工作流特定的参数说明和使用示例

### 2. 多级帮助
- **完整帮助** (`--help`): 详细的参数说明和示例
- **简化帮助** (`--h`): 常用参数和快速示例

### 3. 易于使用
- 简洁的触发方式，符合用户习惯
- 与现有参数系统保持一致的语法
- 不与常用词汇冲突，避免误触发

### 4. 可扩展性
- 模块化的帮助内容生成
- 易于添加新的工作流帮助内容
- 支持自定义帮助格式

## ⚠️ 注意事项

1. **参数优先级**: 帮助请求会优先处理，不会执行实际的工作流
2. **保留原文**: 帮助参数不会从用户输入中移除，保持完整性
3. **错误处理**: 包含完善的异常处理，确保系统稳定性
4. **性能考虑**: 帮助处理轻量级，不会影响正常工作流性能

## 🔄 与现有功能的关系

### 与传统命令帮助的区别
- **传统命令**: `!help`、`/help` - 系统级帮助
- **参数帮助**: `--help`、`--h` - 工作流参数帮助

### 与其他参数的兼容性
- 可以与其他参数混合使用
- 帮助请求优先级高于其他参数
- 不影响现有参数的解析逻辑

## 📈 未来扩展

1. **交互式帮助**: 支持分步骤的交互式帮助指导
2. **个性化帮助**: 根据用户历史使用情况提供个性化建议
3. **多语言支持**: 支持多种语言的帮助内容
4. **动态帮助**: 根据系统状态动态调整帮助内容

---

这个帮助功能的设计遵循了用户友好、技术先进、易于维护的原则，为用户提供了便捷的参数查询方式。
