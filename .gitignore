/config.py
.idea/
__pycache__/
# 数据库文件
database.db
data/langbot.db
data/langbot_backup.db
data/*.db
langbot.log
/banlist.py
/plugins/
!/plugins/__init__.py
/revcfg.py
prompts/
logs/
sensitive.json
temp/
current_tag
scenario/
!scenario/default-template.json
override.json
cookies.json
cmdpriv.json
tips.py
venv*
bin/
.vscode
test_*
venv/
hugchat.json
qcapi
claude.json
bard.json
/*yaml
!.pre-commit-config.yaml
!components.yaml
!/docker-compose.yaml
.DS_Store
/data/storage/
botpy.log*
/poc
/libs/wecom_api/test.py
/venv
test.py
/web_ui
.venv/
uv.lock
WeChatPadPro/