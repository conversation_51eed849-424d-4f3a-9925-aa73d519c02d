"""
[二次开发] 工作流管理器基类
定义工作流管理器的通用接口和数据结构

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：定义工作流管理器的基础协议和结果数据结构
- 维护者：开发团队
- 最后更新：2025-07-15
- 相关任务：工作流管理系统重构
- 依赖关系：被各种工作流管理器实现类依赖
"""

from typing import Protocol, Any, Dict, Optional
from dataclasses import dataclass

@dataclass
class WorkflowResult:
    success: bool
    image_data: Optional[bytes] = None
    text_data: Optional[str] = None  # 新增：支持文本输出（如图片反推结果）
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class BaseWorkflowManager(Protocol):
    async def execute_workflow(self, prompt: str, query: Any) -> WorkflowResult:
        ...
    async def get_generation_info(self, task_id: str) -> WorkflowResult:
        ...
    async def close(self) -> None:
        ...